{"compilerOptions": {"target": "es2020", "lib": ["es2020"], "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "types": ["node"]}, "include": ["src/**/*", "target/types/**/*", "target/idl/**/*"], "exclude": ["node_modules", "dist", "target/debug", "target/release", "target/deploy", "tests"]}