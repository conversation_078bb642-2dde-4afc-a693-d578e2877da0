{"compilerOptions": {"target": "es2020", "lib": ["es2020"], "module": "commonjs", "moduleResolution": "node", "outDir": "./dist", "rootDir": ".", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "sourceMap": true, "types": ["node", "jest"], "typeRoots": ["./types", "./node_modules/@types"]}, "include": ["src/**/*", "tests/**/*", "types/**/*", "target/types/**/*", "target/idl/**/*"], "exclude": ["node_modules", "dist", "target/debug", "target/release", "target/deploy"]}