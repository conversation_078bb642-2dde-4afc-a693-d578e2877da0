[toolchain]
package_manager = "pnpm"

[features]
resolution = true
skip-lint = false

[programs.localnet]
# basic = "JAVuBXeBZqXNtS73azhBDAoYaaAFfo4gWXoZe2e7Jf8H"
vesting = "5D52qmDeLYqVCbWddfRCD5pR3v9MwTqvkwkMywFiAEmy"

[registry]
url = "https://api.apr.dev"

[provider]
cluster = "localnet"
# cluster = "devnet"
wallet = "~/.config/solana/id.json"

[scripts]
test = "../node_modules/.bin/jest --preset ts-jest"
