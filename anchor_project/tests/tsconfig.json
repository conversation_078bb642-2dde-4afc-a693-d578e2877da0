{"extends": "../tsconfig.json", "compilerOptions": {"target": "es2020", "lib": ["es2020"], "module": "commonjs", "moduleResolution": "node", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "types": ["node", "jest"], "noEmit": true}, "include": ["**/*.ts", "../target/types/**/*", "../target/idl/**/*"], "exclude": ["node_modules"]}