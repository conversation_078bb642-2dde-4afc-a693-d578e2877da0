{"address": "9EaFVdWxtmUro5U23yde2qezeL1LfbRnS4xuwSNDUWND", "metadata": {"name": "vesting", "version": "0.1.0", "spec": "0.1.0", "description": "Created with <PERSON><PERSON>"}, "instructions": [{"name": "claim_tokens", "discriminator": [108, 216, 210, 231, 0, 212, 42, 64], "accounts": [{"name": "beneficiary", "writable": true, "signer": true, "relations": ["employee_account"]}, {"name": "employee_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 109, 112, 108, 111, 121, 101, 101, 95, 118, 101, 115, 116, 105, 110, 103]}, {"kind": "account", "path": "beneficiary"}, {"kind": "account", "path": "vesting_account"}]}}, {"name": "vesting_account", "writable": true, "pda": {"seeds": [{"kind": "arg", "path": "company_name"}]}, "relations": ["employee_account"]}, {"name": "mint", "relations": ["vesting_account"]}, {"name": "treasury_token_account", "writable": true, "relations": ["vesting_account"]}, {"name": "employee_token_account", "writable": true, "pda": {"seeds": [{"kind": "account", "path": "beneficiary"}, {"kind": "account", "path": "token_program"}, {"kind": "account", "path": "mint"}], "program": {"kind": "const", "value": [140, 151, 37, 143, 78, 36, 137, 241, 187, 61, 16, 41, 20, 142, 13, 131, 11, 90, 19, 153, 218, 255, 16, 132, 4, 142, 123, 216, 219, 233, 248, 89]}}}, {"name": "token_program"}, {"name": "associated_token_program", "address": "ATokenGPvbdGVxr1b2hvZbsiqW5xWH25efTNsLJA8knL"}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "company_name", "type": "string"}]}, {"name": "create_employee_vesting", "discriminator": [213, 201, 100, 57, 56, 236, 201, 124], "accounts": [{"name": "owner", "writable": true, "signer": true, "relations": ["vesting_account"]}, {"name": "beneficiary"}, {"name": "vesting_account"}, {"name": "employee_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [101, 109, 112, 108, 111, 121, 101, 101, 95, 118, 101, 115, 116, 105, 110, 103]}, {"kind": "account", "path": "beneficiary"}, {"kind": "account", "path": "vesting_account"}]}}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "start_time", "type": "i64"}, {"name": "end_time", "type": "i64"}, {"name": "total_amount", "type": "i64"}, {"name": "cliff_time", "type": "i64"}]}, {"name": "create_vesting_account", "discriminator": [129, 178, 2, 13, 217, 172, 230, 218], "accounts": [{"name": "signer", "writable": true, "signer": true}, {"name": "vesting_account", "writable": true, "pda": {"seeds": [{"kind": "arg", "path": "company_name"}]}}, {"name": "mint"}, {"name": "treasury_token_account", "writable": true, "pda": {"seeds": [{"kind": "const", "value": [118, 101, 115, 116, 105, 110, 103, 95, 116, 114, 101, 97, 115, 117, 114, 121]}, {"kind": "arg", "path": "company_name"}]}}, {"name": "token_program"}, {"name": "system_program", "address": "11111111111111111111111111111111"}], "args": [{"name": "company_name", "type": "string"}]}], "accounts": [{"name": "Employee<PERSON><PERSON>unt", "discriminator": [65, 245, 87, 188, 58, 86, 209, 151]}, {"name": "VestingAccount", "discriminator": [102, 73, 10, 233, 200, 188, 228, 216]}], "errors": [{"code": 6000, "name": "ClaimNotAvailable", "msg": "Claiming is not available yet."}, {"code": 6001, "name": "NothingToClaim", "msg": "There is nothing to claim."}], "types": [{"name": "Employee<PERSON><PERSON>unt", "type": {"kind": "struct", "fields": [{"name": "vesting_account", "type": "pubkey"}, {"name": "beneficiary", "type": "pubkey"}, {"name": "start_time", "type": "i64"}, {"name": "end_time", "type": "i64"}, {"name": "total_amount", "type": "i64"}, {"name": "total_withdrawn", "type": "i64"}, {"name": "cliff_time", "type": "i64"}, {"name": "bump", "type": "u8"}]}}, {"name": "VestingAccount", "type": {"kind": "struct", "fields": [{"name": "owner", "type": "pubkey"}, {"name": "mint", "type": "pubkey"}, {"name": "treasury_token_account", "type": "pubkey"}, {"name": "treasury_bump", "type": "u8"}, {"name": "bump", "type": "u8"}, {"name": "company_name", "type": "string"}]}}]}